<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Brain icon with gradient effect -->
    <group android:translateX="54" android:translateY="54">
        <!-- Main brain shape -->
        <path
            android:fillColor="#c039ff"
            android:pathData="M0,-20C-12,-20 -22,-10 -22,2C-22,6 -21,9 -19,12C-21,14 -22,17 -22,20C-22,26 -17,31 -11,31C-9,31 -7,30 -5,29C-4,30 -2,31 0,31C2,31 4,30 5,29C7,30 9,31 11,31C17,31 22,26 22,20C22,17 21,14 19,12C21,9 22,6 22,2C22,-10 12,-20 0,-20Z"/>
        
        <!-- Eyes -->
        <circle
            android:fillColor="#ffffff"
            android:cx="-6"
            android:cy="-8"
            android:r="2"/>
        <circle
            android:fillColor="#ffffff"
            android:cx="6"
            android:cy="-8"
            android:r="2"/>
        
        <!-- Brain details -->
        <path
            android:fillColor="#ffffff"
            android:fillAlpha="0.3"
            android:pathData="M-8,0C-6,2 -4,4 -2,6C0,4 2,2 4,0C6,2 8,4 10,6"/>
    </group>
</vector>
