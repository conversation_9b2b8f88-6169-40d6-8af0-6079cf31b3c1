<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Application as soon as it is started.
         This theme is visible to the user while the Flutter UI initializes.
         After that, this theme continues to determine the Window background
         behind the Flutter UI. -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             Flutter draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">#0d1117</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
    
    <!-- Theme applied to the Android Application after the splash screen is removed. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">#0d1117</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
</resources>
